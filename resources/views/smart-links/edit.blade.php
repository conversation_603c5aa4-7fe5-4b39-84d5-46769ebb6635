@extends('layouts.app')

@section('title', 'Chỉnh sửa Link Smart')

@push('styles')
<style>
.schedule-item {
    transition: all 0.3s ease;
}
.schedule-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.card-header {
    border-bottom: 2px solid #dee2e6;
}
.form-label.fw-bold {
    color: #495057;
}
#generated-link {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #198754;
}
.copy-btn:hover {
    transform: scale(1.05);
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Chỉnh sửa Link Smart
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('smart-links.update', $smartLink) }}" method="POST" id="smart-link-form">
                        @csrf
                        @method('PUT')

                        <div class="mb-3">
                            <label for="title" class="form-label">Tiêu đề <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror"
                                   id="title" name="title" value="{{ old('title', $smartLink->title) }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Mô tả</label>
                            <textarea class="form-control @error('description') is-invalid @enderror"
                                      id="description" name="description" rows="2">{{ old('description', $smartLink->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                       value="1" {{ old('is_active', $smartLink->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label fw-bold" for="is_active">
                                    <i class="fas fa-toggle-on me-1"></i>Kích hoạt Link Smart
                                </label>
                            </div>
                        </div>

                        <!-- Smart Link Display -->
                        <div class="card border-success mb-4">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0"><i class="fas fa-link me-2"></i>Link Smart hiện tại</h6>
                            </div>
                            <div class="card-body">
                                <div class="input-group">
                                    <input type="text" class="form-control form-control-lg" id="current-link"
                                           value="{{ $smartLink->full_url }}" readonly>
                                    <button class="btn btn-outline-success copy-btn" type="button"
                                            data-clipboard-target="#current-link" title="Sao chép link">
                                        <i class="fas fa-copy me-1"></i>Sao chép
                                    </button>
                                </div>
                                <div class="alert alert-info mt-3 mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Lưu ý:</strong> Mã link không thể thay đổi sau khi tạo. Chỉ có thể cập nhật lịch trình và thông tin.
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Lịch trình Webinar <span class="text-danger">*</span></h6>
                                    <div>
                                        <small class="text-muted me-2">{{ $webinars->count() }} webinar có sẵn</small>
                                        <button type="button" class="btn btn-sm btn-primary" id="add-schedule">
                                            <i class="fas fa-plus me-1"></i>Thêm lịch trình
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="schedules-container">
                                    <!-- Existing schedules will be loaded here -->
                                </div>
                            </div>
                        </div>

                        <div class="mt-4 d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ route('smart-links.show', $smartLink) }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Quay lại
                            </a>
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-save me-1"></i>Cập nhật Link Smart
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.8/clipboard.min.js"></script>
<script>
let scheduleIndex = 0;

// Initialize clipboard
var clipboard = new ClipboardJS('.copy-btn');
clipboard.on('success', function(e) {
    const button = e.trigger;
    const originalIcon = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check text-success"></i>';
    setTimeout(() => {
        button.innerHTML = originalIcon;
    }, 2000);
    e.clearSelection();
});

// Schedule template
function getScheduleTemplate(index, schedule = null) {
    const webinarsData = @json($webinars->map(function($webinar) {
        return [
            'id' => $webinar->id,
            'title' => $webinar->title,
            'schedules' => $webinar->schedules ?? []
        ];
    }));

    let webinarOptions = '<option value="">Chọn webinar...</option>';
    webinarsData.forEach(webinar => {
        const selected = schedule && schedule.webinar_id == webinar.id ? 'selected' : '';
        webinarOptions += `<option value="${webinar.id}" ${selected}>${webinar.title}</option>`;
    });

    return `
        <div class="schedule-item card border-primary mb-3" data-index="${index}">
            <div class="card-header bg-light">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0 text-primary"><i class="fas fa-calendar me-2"></i>Lịch trình #${index + 1}</h6>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-schedule">
                        <i class="fas fa-trash me-1"></i>Xóa
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-video me-1"></i>Webinar <span class="text-danger">*</span>
                            </label>
                            <select class="form-select webinar-select" name="schedules[${index}][webinar_id]" required onchange="updateScheduleOptions(${index})">
                                ${webinarOptions}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-clock me-1"></i>Lịch của webinar <span class="text-danger">*</span>
                            </label>
                            <select class="form-select schedule-select" name="schedules[${index}][schedule_index]" required>
                                <option value="">Chọn webinar trước...</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Update schedule options when webinar is selected
function updateScheduleOptions(index) {
    const webinarSelect = document.querySelector(`select[name="schedules[${index}][webinar_id]"]`);
    const scheduleSelect = document.querySelector(`select[name="schedules[${index}][schedule_index]"]`);

    const webinarId = webinarSelect.value;
    scheduleSelect.innerHTML = '<option value="">Chọn lịch...</option>';

    if (webinarId) {
        const webinarsData = @json($webinars->map(function($webinar) {
            return [
                'id' => $webinar->id,
                'title' => $webinar->title,
                'schedules' => $webinar->schedules ?? []
            ];
        }));

        const webinar = webinarsData.find(w => w.id == webinarId);
        if (webinar && webinar.schedules && webinar.schedules.length > 0) {
            webinar.schedules.forEach((schedule, idx) => {
                const scheduleText = `${schedule.date} - ${schedule.time}`;
                scheduleSelect.innerHTML += `<option value="${idx}">${scheduleText}</option>`;
            });
            scheduleSelect.disabled = false;
        } else {
            scheduleSelect.innerHTML = '<option value="">Webinar chưa có lịch</option>';
            scheduleSelect.disabled = true;
        }
    } else {
        scheduleSelect.disabled = true;
    }
}

// Add schedule
document.getElementById('add-schedule').addEventListener('click', function() {
    const container = document.getElementById('schedules-container');
    container.insertAdjacentHTML('beforeend', getScheduleTemplate(scheduleIndex));
    scheduleIndex++;
});

// Remove schedule
document.addEventListener('click', function(e) {
    if (e.target.closest('.remove-schedule')) {
        const scheduleItem = e.target.closest('.schedule-item');
        scheduleItem.remove();
        updateScheduleNumbers();
    }
});

// Update schedule numbers
function updateScheduleNumbers() {
    const scheduleItems = document.querySelectorAll('.schedule-item');
    scheduleItems.forEach((item, index) => {
        const title = item.querySelector('h6');
        title.textContent = `Lịch trình #${index + 1}`;
        item.setAttribute('data-index', index);

        const webinarSelect = item.querySelector('.webinar-select');
        const scheduleSelect = item.querySelector('.schedule-select');

        webinarSelect.setAttribute('name', `schedules[${index}][webinar_id]`);
        webinarSelect.setAttribute('onchange', `updateScheduleOptions(${index})`);
        scheduleSelect.setAttribute('name', `schedules[${index}][schedule_index]`);
    });
}

// Load existing schedules
document.addEventListener('DOMContentLoaded', function() {
    const existingSchedules = @json($smartLink->schedules);

    existingSchedules.forEach((schedule, index) => {
        const container = document.getElementById('schedules-container');

        const scheduleData = {
            webinar_id: schedule.webinar_id,
            schedule_index: schedule.schedule_index
        };

        container.insertAdjacentHTML('beforeend', getScheduleTemplate(index, scheduleData));

        // Set the schedule index after webinar is selected
        setTimeout(() => {
            const scheduleSelect = document.querySelector(`select[name="schedules[${index}][schedule_index]"]`);
            if (scheduleSelect) {
                updateScheduleOptions(index);
                setTimeout(() => {
                    scheduleSelect.value = schedule.schedule_index;
                }, 100);
            }
        }, 100);

        scheduleIndex++;
    });
});

// Form validation
document.getElementById('smart-link-form').addEventListener('submit', function(e) {
    const schedules = document.querySelectorAll('.schedule-item');
    if (schedules.length === 0) {
        e.preventDefault();
        alert('Vui lòng thêm ít nhất một lịch trình!');
        return false;
    }

    let hasError = false;
    schedules.forEach((schedule, index) => {
        const webinarId = schedule.querySelector('select[name*="[webinar_id]"]').value;
        const scheduleIndex = schedule.querySelector('select[name*="[schedule_index]"]').value;

        if (!webinarId || scheduleIndex === '') {
            hasError = true;
        }
    });

    if (hasError) {
        e.preventDefault();
        alert('Vui lòng chọn đầy đủ webinar và lịch cho tất cả lịch trình!');
        return false;
    }
});
</script>
@endpush
@endsection
