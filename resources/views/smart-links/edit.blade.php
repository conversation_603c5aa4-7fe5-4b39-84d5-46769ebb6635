@extends('layouts.app')

@section('title', 'Chỉnh sửa Link Smart')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Chỉnh sửa Link Smart
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('smart-links.update', $smartLink) }}" method="POST" id="smart-link-form">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="title" class="form-label">Tiêu đề <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('title') is-invalid @enderror"
                                           id="title" name="title" value="{{ old('title', $smartLink->title) }}" required>
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="description" class="form-label">Mô tả</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror"
                                              id="description" name="description" rows="3">{{ old('description', $smartLink->description) }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                               {{ old('is_active', $smartLink->is_active) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            Kích hoạt Link Smart
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Link Smart</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" value="{{ $smartLink->full_url }}" readonly>
                                        <button class="btn btn-outline-secondary copy-btn" type="button"
                                                data-clipboard-text="{{ $smartLink->full_url }}" title="Sao chép">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">Lịch trình Webinar <span class="text-danger">*</span></h6>
                            <button type="button" class="btn btn-sm btn-outline-primary" id="add-schedule">
                                <i class="fas fa-plus me-1"></i>Thêm lịch trình
                            </button>
                        </div>

                        <div id="schedules-container">
                            <!-- Existing schedules will be loaded here -->
                        </div>

                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Cập nhật Link Smart
                            </button>
                            <a href="{{ route('smart-links.show', $smartLink) }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>Hủy
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.8/clipboard.min.js"></script>
<script>
let scheduleIndex = 0;

// Initialize clipboard
var clipboard = new ClipboardJS('.copy-btn');
clipboard.on('success', function(e) {
    const button = e.trigger;
    const originalIcon = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check text-success"></i>';
    setTimeout(() => {
        button.innerHTML = originalIcon;
    }, 2000);
    e.clearSelection();
});

// Schedule template
function getScheduleTemplate(index, schedule = null) {
    const webinarOptions = @json($webinars->map(function($webinar) {
        return ['id' => $webinar->id, 'title' => $webinar->title];
    }));

    let optionsHtml = '<option value="">Chọn webinar...</option>';
    webinarOptions.forEach(webinar => {
        const selected = schedule && schedule.webinar_id == webinar.id ? 'selected' : '';
        optionsHtml += `<option value="${webinar.id}" ${selected}>${webinar.title}</option>`;
    });

    return `
        <div class="schedule-item border rounded p-3 mb-3" data-index="${index}">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0">Lịch trình #${index + 1}</h6>
                <button type="button" class="btn btn-sm btn-outline-danger remove-schedule">
                    <i class="fas fa-trash"></i>
                </button>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Webinar <span class="text-danger">*</span></label>
                        <select class="form-select" name="schedules[${index}][webinar_id]" required>
                            ${optionsHtml}
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Mô tả lịch trình</label>
                        <input type="text" class="form-control" name="schedules[${index}][description]"
                               placeholder="VD: Buổi 1 - Giới thiệu" value="${schedule ? schedule.description || '' : ''}">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Thời gian bắt đầu <span class="text-danger">*</span></label>
                        <input type="datetime-local" class="form-control" name="schedules[${index}][start_date]"
                               value="${schedule ? schedule.start_date : ''}" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Thời gian kết thúc <span class="text-danger">*</span></label>
                        <input type="datetime-local" class="form-control" name="schedules[${index}][end_date]"
                               value="${schedule ? schedule.end_date : ''}" required>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Add schedule
document.getElementById('add-schedule').addEventListener('click', function() {
    const container = document.getElementById('schedules-container');
    container.insertAdjacentHTML('beforeend', getScheduleTemplate(scheduleIndex));
    scheduleIndex++;
});

// Remove schedule
document.addEventListener('click', function(e) {
    if (e.target.closest('.remove-schedule')) {
        const scheduleItem = e.target.closest('.schedule-item');
        scheduleItem.remove();
        updateScheduleNumbers();
    }
});

// Update schedule numbers
function updateScheduleNumbers() {
    const scheduleItems = document.querySelectorAll('.schedule-item');
    scheduleItems.forEach((item, index) => {
        const title = item.querySelector('h6');
        title.textContent = `Lịch trình #${index + 1}`;
        item.setAttribute('data-index', index);

        const inputs = item.querySelectorAll('input, select');
        inputs.forEach(input => {
            const name = input.getAttribute('name');
            if (name) {
                const newName = name.replace(/schedules\[\d+\]/, `schedules[${index}]`);
                input.setAttribute('name', newName);
            }
        });
    });
}

// Load existing schedules
document.addEventListener('DOMContentLoaded', function() {
    const existingSchedules = @json($smartLink->schedules);

    existingSchedules.forEach((schedule, index) => {
        const container = document.getElementById('schedules-container');

        // Format dates for datetime-local input
        const startDate = new Date(schedule.start_date).toISOString().slice(0, 16);
        const endDate = new Date(schedule.end_date).toISOString().slice(0, 16);

        const scheduleData = {
            webinar_id: schedule.webinar_id,
            description: schedule.description,
            start_date: startDate,
            end_date: endDate
        };

        container.insertAdjacentHTML('beforeend', getScheduleTemplate(index, scheduleData));
        scheduleIndex++;
    });

    // Webinar selections are already set in the template
});

// Form validation
document.getElementById('smart-link-form').addEventListener('submit', function(e) {
    const schedules = document.querySelectorAll('.schedule-item');
    if (schedules.length === 0) {
        e.preventDefault();
        alert('Vui lòng thêm ít nhất một lịch trình!');
        return false;
    }

    let hasError = false;
    schedules.forEach(schedule => {
        const startDate = schedule.querySelector('input[name*="[start_date]"]').value;
        const endDate = schedule.querySelector('input[name*="[end_date]"]').value;

        if (startDate && endDate && new Date(startDate) >= new Date(endDate)) {
            hasError = true;
        }
    });

    if (hasError) {
        e.preventDefault();
        alert('Thời gian kết thúc phải sau thời gian bắt đầu!');
        return false;
    }
});
</script>
@endpush
@endsection
