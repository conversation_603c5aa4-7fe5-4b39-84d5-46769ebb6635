@extends('layouts.app')

@section('title', 'Chi tiết Link Smart')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-link me-2"></i>Chi tiết Link Smart
                    </h5>
                    <div>
                        <a href="{{ route('smart-links.edit', $smartLink) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit me-1"></i>Chỉnh sửa
                        </a>
                        <a href="{{ route('smart-links.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>Quay lại
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Thông tin cơ bản</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td width="30%"><strong>Tiêu đề:</strong></td>
                                    <td>{{ $smartLink->title }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Mô tả:</strong></td>
                                    <td>{{ $smartLink->description ?: 'Không có' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Trạng thái:</strong></td>
                                    <td>
                                        @if($smartLink->is_active)
                                            <span class="badge badge-success">Hoạt động</span>
                                        @else
                                            <span class="badge badge-secondary">Tạm dừng</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Link Smart:</strong></td>
                                    <td>
                                        <div class="input-group">
                                            <input type="text" class="form-control form-control-sm"
                                                   value="{{ $smartLink->full_url }}"
                                                   id="smart-url" readonly>
                                            <button class="btn btn-outline-secondary btn-sm copy-btn" type="button"
                                                    data-clipboard-target="#smart-url" title="Sao chép">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>Thống kê</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td width="40%"><strong>Số lượt click:</strong></td>
                                    <td><span class="badge badge-info">{{ number_format($smartLink->click_count) }}</span></td>
                                </tr>
                                <tr>
                                    <td><strong>Lần truy cập cuối:</strong></td>
                                    <td>
                                        @if($smartLink->last_accessed_at)
                                            {{ $smartLink->last_accessed_at->format('d/m/Y H:i:s') }}
                                        @else
                                            <span class="text-muted">Chưa có</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Ngày tạo:</strong></td>
                                    <td>{{ $smartLink->created_at->format('d/m/Y H:i:s') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Cập nhật cuối:</strong></td>
                                    <td>{{ $smartLink->updated_at->format('d/m/Y H:i:s') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <hr>

                    <h6>Lịch trình Webinar</h6>
                    @if($smartLink->schedules->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>STT</th>
                                        <th>Webinar</th>
                                        <th>Lịch trình</th>
                                        <th>Thời gian bắt đầu</th>
                                        <th>Thời gian kết thúc</th>
                                        <th>Trạng thái</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($smartLink->schedules as $index => $schedule)
                                        @php
                                            $scheduleData = $schedule->schedule_data;
                                            $startDateTime = $schedule->start_date_time;
                                            $endDateTime = $schedule->end_date_time;
                                        @endphp
                                        <tr class="@if($schedule->isActive()) table-success @elseif($schedule->hasEnded()) table-secondary @endif">
                                            <td>{{ $index + 1 }}</td>
                                            <td>
                                                <strong>{{ $schedule->webinar->title }}</strong>
                                                <br>
                                                <small class="text-muted">{{ $schedule->webinar->speaker }}</small>
                                            </td>
                                            <td>
                                                @if($scheduleData)
                                                    {{ $scheduleData['date'] }} - {{ $scheduleData['time'] }}
                                                @else
                                                    <span class="text-muted">Lịch không tồn tại</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($startDateTime)
                                                    {{ $startDateTime->format('d/m/Y H:i') }}
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($endDateTime)
                                                    {{ $endDateTime->format('d/m/Y H:i') }}
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge {{ $schedule->status_badge_class }}">
                                                    {{ $schedule->status }}
                                                </span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Chưa có lịch trình nào được thiết lập.
                        </div>
                    @endif

                    <hr>

                    <h6>Trạng thái hiện tại</h6>
                    <div class="row">
                        <div class="col-md-6">
                            @php
                                $currentWebinar = $smartLink->getCurrentWebinar();
                            @endphp
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">Webinar hiện tại</h6>
                                </div>
                                <div class="card-body">
                                    @if($currentWebinar)
                                        <h6>{{ $currentWebinar->title }}</h6>
                                        <p class="mb-1"><strong>Diễn giả:</strong> {{ $currentWebinar->speaker }}</p>
                                        <p class="mb-0">
                                            <a href="{{ $currentWebinar->join_url }}" target="_blank" class="btn btn-sm btn-success">
                                                <i class="fas fa-external-link-alt me-1"></i>Truy cập webinar
                                            </a>
                                        </p>
                                    @else
                                        <p class="text-muted mb-0">Không có webinar nào đang diễn ra</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            @php
                                $nextWebinar = $smartLink->getNextWebinar();
                            @endphp
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">Webinar tiếp theo</h6>
                                </div>
                                <div class="card-body">
                                    @if($nextWebinar)
                                        <h6>{{ $nextWebinar->title }}</h6>
                                        <p class="mb-1"><strong>Diễn giả:</strong> {{ $nextWebinar->speaker }}</p>
                                        @php
                                            $nextSchedule = $smartLink->schedules->filter(function($schedule) use ($nextWebinar) {
                                                return $schedule->webinar_id == $nextWebinar->id && $schedule->isUpcoming();
                                            })->first();
                                        @endphp
                                        @if($nextSchedule && $nextSchedule->start_date_time)
                                            <p class="mb-0"><strong>Bắt đầu:</strong> {{ $nextSchedule->start_date_time->format('d/m/Y H:i') }}</p>
                                        @endif
                                    @else
                                        <p class="text-muted mb-0">Không có webinar nào sắp tới</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.8/clipboard.min.js"></script>
<script>
    // Initialize clipboard
    var clipboard = new ClipboardJS('.copy-btn');

    clipboard.on('success', function(e) {
        // Show success message
        const button = e.trigger;
        const originalIcon = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check text-success"></i>';

        setTimeout(() => {
            button.innerHTML = originalIcon;
        }, 2000);

        e.clearSelection();
    });
</script>
@endpush
@endsection
