@extends('layouts.app')

@section('title', '<PERSON> tiết <PERSON>')

@push('styles')
<style>
.info-card {
    transition: all 0.3s ease;
}
.info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.smart-link-url {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #198754;
    font-size: 1.1em;
}
.status-badge {
    font-size: 0.9em;
    padding: 0.5em 1em;
}
.copy-btn:hover {
    transform: scale(1.05);
}
.stats-card {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}
.stats-card .card-body {
    padding: 1.5rem;
}
.stats-card h3 {
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}
.stats-card .text-white-50 {
    color: rgba(255,255,255,0.8) !important;
    font-weight: 500;
}
.stats-card .border-white-50 {
    border-color: rgba(255,255,255,0.3) !important;
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-link me-2"></i>Chi tiết Link Smart
                    </h5>
                    <div>
                        <a href="{{ route('smart-links.edit', $smartLink) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit me-1"></i>Chỉnh sửa
                        </a>
                        <a href="{{ route('smart-links.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>Quay lại
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Smart Link URL Card -->
                    <div class="card border-success mb-4">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0"><i class="fas fa-link me-2"></i>Link Smart của bạn</h6>
                        </div>
                        <div class="card-body">
                            <div class="input-group input-group-lg">
                                <input type="text" class="form-control smart-link-url" id="smart-link-url"
                                       value="{{ $smartLink->full_url }}" readonly>
                                <button class="btn btn-outline-success copy-btn" type="button"
                                        data-clipboard-target="#smart-link-url" title="Sao chép link">
                                    <i class="fas fa-copy me-1"></i>Sao chép
                                </button>
                                <a href="{{ $smartLink->full_url }}" target="_blank" class="btn btn-primary" title="Mở link">
                                    <i class="fas fa-external-link-alt me-1"></i>Mở
                                </a>
                            </div>
                            <div class="alert alert-info mt-3 mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Cách hoạt động:</strong> Link này sẽ tự động chuyển hướng đến webinar phù hợp dựa trên thời gian hiện tại.
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Thông tin cơ bản -->
                        <div class="col-md-6">
                            <div class="card info-card h-100">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Thông tin cơ bản</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-muted">Tiêu đề</label>
                                        <p class="mb-0">{{ $smartLink->title }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-muted">Mô tả</label>
                                        <p class="mb-0">{{ $smartLink->description ?: 'Không có mô tả' }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-muted">Trạng thái</label>
                                        <div>
                                            @if($smartLink->is_active)
                                                <span class="badge bg-success status-badge">
                                                    <i class="fas fa-check-circle me-1"></i>Đang hoạt động
                                                </span>
                                            @else
                                                <span class="badge bg-secondary status-badge">
                                                    <i class="fas fa-pause-circle me-1"></i>Tạm dừng
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="mb-0">
                                        <label class="form-label fw-bold text-muted">Mã Link</label>
                                        <p class="mb-0"><code>{{ $smartLink->code }}</code></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Thống kê -->
                        <div class="col-md-6">
                            <div class="card stats-card h-100">
                                <div class="card-header border-0">
                                    <h6 class="mb-0 text-white"><i class="fas fa-chart-bar me-2"></i>Thống kê</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="mb-3">
                                                <h2 class="mb-1 fw-bold">{{ number_format($smartLink->click_count) }}</h2>
                                                <div class="text-white-50 fw-semibold">Lượt click</div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="mb-3">
                                                <h2 class="mb-1 fw-bold">{{ $smartLink->schedules->count() }}</h2>
                                                <div class="text-white-50 fw-semibold">Lịch trình</div>
                                            </div>
                                        </div>
                                    </div>
                                    <hr class="border-white-50">
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="text-white-50 fw-semibold mb-1">Ngày tạo</div>
                                            <div class="fw-bold">{{ $smartLink->created_at->format('d/m/Y') }}</div>
                                        </div>
                                        <div class="col-6">
                                            <div class="text-white-50 fw-semibold mb-1">Lần truy cập cuối</div>
                                            <div class="fw-bold">
                                                @if($smartLink->last_accessed_at)
                                                    {{ $smartLink->last_accessed_at->format('d/m/Y H:i') }}
                                                @else
                                                    Chưa có
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Lịch trình Webinar -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Lịch trình Webinar</h6>
                        </div>
                        <div class="card-body">
                            @if($smartLink->schedules->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>STT</th>
                                                <th>Webinar</th>
                                                <th>Lịch trình</th>
                                                <th>Thời gian bắt đầu</th>
                                                <th>Thời gian kết thúc</th>
                                                <th>Trạng thái</th>
                                            </tr>
                                        </thead>
                                            @foreach($smartLink->schedules as $index => $schedule)
                                                @php
                                                    $scheduleData = $schedule->schedule_data;
                                                    $startDateTime = $schedule->start_date_time;
                                                    $endDateTime = $schedule->end_date_time;
                                                @endphp
                                                <tr class="@if($schedule->isActive()) table-success @elseif($schedule->hasEnded()) table-secondary @endif">
                                                    <td>{{ $index + 1 }}</td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <i class="fas fa-video text-primary me-2"></i>
                                                            <div>
                                                                <strong>{{ $schedule->webinar->title }}</strong>
                                                                <br>
                                                                <small class="text-muted">{{ $schedule->webinar->speaker }}</small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        @if($scheduleData)
                                                            <span class="badge bg-info">{{ $scheduleData['date'] }} - {{ $scheduleData['time'] }}</span>
                                                        @else
                                                            <span class="text-muted">Lịch không tồn tại</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        @if($startDateTime)
                                                            <i class="fas fa-play text-success me-1"></i>{{ $startDateTime->format('d/m/Y H:i') }}
                                                        @else
                                                            <span class="text-muted">-</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        @if($endDateTime)
                                                            <i class="fas fa-stop text-danger me-1"></i>{{ $endDateTime->format('d/m/Y H:i') }}
                                                        @else
                                                            <span class="text-muted">-</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <span class="badge {{ $schedule->status_badge_class }}">
                                                            {{ $schedule->status }}
                                                        </span>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    Chưa có lịch trình nào được thiết lập.
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Webinar hiện tại và sắp tới -->
                    <div class="row mt-4">
                        @php
                            $currentWebinar = $smartLink->getCurrentWebinar();
                            $nextWebinar = $smartLink->getNextWebinar();
                        @endphp

                        @if($currentWebinar)
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0"><i class="fas fa-play-circle me-2"></i>Webinar hiện tại</h6>
                                    </div>
                                    <div class="card-body">
                                        <h5 class="text-success">{{ $currentWebinar->title }}</h5>
                                        <p class="mb-2"><i class="fas fa-user me-2"></i><strong>Diễn giả:</strong> {{ $currentWebinar->speaker }}</p>
                                        <a href="{{ $currentWebinar->join_url }}" target="_blank" class="btn btn-success">
                                            <i class="fas fa-external-link-alt me-1"></i>Tham gia ngay
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if($nextWebinar)
                            <div class="col-md-6">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-dark">
                                        <h6 class="mb-0"><i class="fas fa-clock me-2"></i>Webinar sắp tới</h6>
                                    </div>
                                    <div class="card-body">
                                        <h5 class="text-warning">{{ $nextWebinar->title }}</h5>
                                        <p class="mb-2"><i class="fas fa-user me-2"></i><strong>Diễn giả:</strong> {{ $nextWebinar->speaker }}</p>
                                        @php
                                            $nextSchedule = $smartLink->schedules->filter(function($schedule) use ($nextWebinar) {
                                                return $schedule->webinar_id == $nextWebinar->id && $schedule->isUpcoming();
                                            })->first();
                                        @endphp
                                        @if($nextSchedule && $nextSchedule->start_date_time)
                                            <p class="mb-0"><strong>Bắt đầu:</strong> {{ $nextSchedule->start_date_time->format('d/m/Y H:i') }}</p>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if(!$currentWebinar && !$nextWebinar)
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Hiện tại không có webinar nào đang diễn ra hoặc sắp diễn ra.
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.8/clipboard.min.js"></script>
<script>
// Initialize clipboard
var clipboard = new ClipboardJS('.copy-btn');
clipboard.on('success', function(e) {
    const button = e.trigger;
    const originalIcon = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check text-success me-1"></i>Đã sao chép!';
    setTimeout(() => {
        button.innerHTML = originalIcon;
    }, 2000);
    e.clearSelection();
});
</script>
@endpush

@endsection
