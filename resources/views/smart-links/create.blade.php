@extends('layouts.app')

@section('title', 'Tạo Link Smart')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-plus me-2"></i>Tạo Link Smart
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('smart-links.store') }}" method="POST" id="smart-link-form">
                        @csrf

                        <div class="mb-3">
                            <label for="title" class="form-label">Ti<PERSON>u đề <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror"
                                   id="title" name="title" value="{{ old('title') }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label"><PERSON><PERSON> tả</label>
                            <textarea class="form-control @error('description') is-invalid @enderror"
                                      id="description" name="description" rows="2">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <hr>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">Lịch trình Webinar <span class="text-danger">*</span></h6>
                            <button type="button" class="btn btn-sm btn-outline-primary" id="add-schedule">
                                <i class="fas fa-plus me-1"></i>Thêm lịch trình
                            </button>
                        </div>

                        <div id="schedules-container">
                            <!-- Schedule items will be added here -->
                        </div>

                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Tạo Link Smart
                            </button>
                            <a href="{{ route('smart-links.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>Hủy
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
let scheduleIndex = 0;
const webinarsData = @json($webinars->map(function($webinar) {
    return [
        'id' => $webinar->id,
        'title' => $webinar->title,
        'schedules' => $webinar->schedules ?? []
    ];
}));

// Debug: Log webinars data
console.log('Webinars Data:', webinarsData);

// Schedule template
function getScheduleTemplate(index) {
    let webinarOptions = '<option value="">Chọn webinar...</option>';
    webinarsData.forEach(webinar => {
        webinarOptions += `<option value="${webinar.id}">${webinar.title}</option>`;
    });

    return `
        <div class="schedule-item border rounded p-3 mb-3" data-index="${index}">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0">Lịch trình #${index + 1}</h6>
                <button type="button" class="btn btn-sm btn-outline-danger remove-schedule">
                    <i class="fas fa-trash"></i>
                </button>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Webinar <span class="text-danger">*</span></label>
                        <select class="form-select webinar-select" name="schedules[${index}][webinar_id]" required onchange="updateScheduleOptions(${index})">
                            ${webinarOptions}
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Lịch của webinar <span class="text-danger">*</span></label>
                        <select class="form-select schedule-select" name="schedules[${index}][schedule_index]" required disabled>
                            <option value="">Chọn webinar trước...</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Update schedule options when webinar is selected
function updateScheduleOptions(index) {
    const webinarSelect = document.querySelector(`select[name="schedules[${index}][webinar_id]"]`);
    const scheduleSelect = document.querySelector(`select[name="schedules[${index}][schedule_index]"]`);

    const webinarId = webinarSelect.value;
    scheduleSelect.innerHTML = '<option value="">Chọn lịch...</option>';

    if (webinarId) {
        const webinar = webinarsData.find(w => w.id == webinarId);
        if (webinar && webinar.schedules && webinar.schedules.length > 0) {
            webinar.schedules.forEach((schedule, idx) => {
                const scheduleText = `${schedule.date} - ${schedule.time}`;
                scheduleSelect.innerHTML += `<option value="${idx}">${scheduleText}</option>`;
            });
            scheduleSelect.disabled = false;
        } else {
            scheduleSelect.innerHTML = '<option value="">Webinar chưa có lịch</option>';
            scheduleSelect.disabled = true;
        }
    } else {
        scheduleSelect.disabled = true;
    }
}

// Add schedule
document.getElementById('add-schedule').addEventListener('click', function() {
    const container = document.getElementById('schedules-container');
    container.insertAdjacentHTML('beforeend', getScheduleTemplate(scheduleIndex));
    scheduleIndex++;
});

// Remove schedule
document.addEventListener('click', function(e) {
    if (e.target.closest('.remove-schedule')) {
        const scheduleItem = e.target.closest('.schedule-item');
        scheduleItem.remove();
        updateScheduleNumbers();
    }
});

// Update schedule numbers
function updateScheduleNumbers() {
    const scheduleItems = document.querySelectorAll('.schedule-item');
    scheduleItems.forEach((item, index) => {
        const title = item.querySelector('h6');
        title.textContent = `Lịch trình #${index + 1}`;
        item.setAttribute('data-index', index);

        // Update input names and onchange handlers
        const webinarSelect = item.querySelector('.webinar-select');
        const scheduleSelect = item.querySelector('.schedule-select');

        webinarSelect.setAttribute('name', `schedules[${index}][webinar_id]`);
        webinarSelect.setAttribute('onchange', `updateScheduleOptions(${index})`);
        scheduleSelect.setAttribute('name', `schedules[${index}][schedule_index]`);
    });
}

// Add first schedule on page load
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('add-schedule').click();
});

// Form validation
document.getElementById('smart-link-form').addEventListener('submit', function(e) {
    const schedules = document.querySelectorAll('.schedule-item');
    if (schedules.length === 0) {
        e.preventDefault();
        alert('Vui lòng thêm ít nhất một lịch trình!');
        return false;
    }

    // Validate that all schedules have webinar and schedule selected
    let hasError = false;
    schedules.forEach((schedule, index) => {
        const webinarId = schedule.querySelector('select[name*="[webinar_id]"]').value;
        const scheduleIndex = schedule.querySelector('select[name*="[schedule_index]"]').value;

        if (!webinarId || scheduleIndex === '') {
            hasError = true;
        }
    });

    if (hasError) {
        e.preventDefault();
        alert('Vui lòng chọn đầy đủ webinar và lịch cho tất cả lịch trình!');
        return false;
    }
});
</script>
@endpush
@endsection
