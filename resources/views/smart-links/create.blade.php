@extends('layouts.app')

@section('title', 'Tạo Link Smart')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-plus me-2"></i>Tạo Link Smart
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('smart-links.store') }}" method="POST" id="smart-link-form">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="title" class="form-label">Tiêu đề <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                           id="title" name="title" value="{{ old('title') }}" required>
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="description" class="form-label">Mô tả</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" 
                                              id="description" name="description" rows="3">{{ old('description') }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">Lịch trình Webinar <span class="text-danger">*</span></h6>
                            <button type="button" class="btn btn-sm btn-outline-primary" id="add-schedule">
                                <i class="fas fa-plus me-1"></i>Thêm lịch trình
                            </button>
                        </div>

                        <div id="schedules-container">
                            <!-- Schedule items will be added here -->
                        </div>

                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Tạo Link Smart
                            </button>
                            <a href="{{ route('smart-links.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>Hủy
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
let scheduleIndex = 0;

// Schedule template
function getScheduleTemplate(index) {
    return `
        <div class="schedule-item border rounded p-3 mb-3" data-index="${index}">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0">Lịch trình #${index + 1}</h6>
                <button type="button" class="btn btn-sm btn-outline-danger remove-schedule">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Webinar <span class="text-danger">*</span></label>
                        <select class="form-select" name="schedules[${index}][webinar_id]" required>
                            <option value="">Chọn webinar...</option>
                            @foreach($webinars as $webinar)
                                <option value="{{ $webinar->id }}">{{ $webinar->title }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Mô tả lịch trình</label>
                        <input type="text" class="form-control" name="schedules[${index}][description]" 
                               placeholder="VD: Buổi 1 - Giới thiệu">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Thời gian bắt đầu <span class="text-danger">*</span></label>
                        <input type="datetime-local" class="form-control" name="schedules[${index}][start_date]" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Thời gian kết thúc <span class="text-danger">*</span></label>
                        <input type="datetime-local" class="form-control" name="schedules[${index}][end_date]" required>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Add schedule
document.getElementById('add-schedule').addEventListener('click', function() {
    const container = document.getElementById('schedules-container');
    container.insertAdjacentHTML('beforeend', getScheduleTemplate(scheduleIndex));
    scheduleIndex++;
});

// Remove schedule
document.addEventListener('click', function(e) {
    if (e.target.closest('.remove-schedule')) {
        const scheduleItem = e.target.closest('.schedule-item');
        scheduleItem.remove();
        
        // Update schedule numbers
        updateScheduleNumbers();
    }
});

// Update schedule numbers
function updateScheduleNumbers() {
    const scheduleItems = document.querySelectorAll('.schedule-item');
    scheduleItems.forEach((item, index) => {
        const title = item.querySelector('h6');
        title.textContent = `Lịch trình #${index + 1}`;
        item.setAttribute('data-index', index);
        
        // Update input names
        const inputs = item.querySelectorAll('input, select');
        inputs.forEach(input => {
            const name = input.getAttribute('name');
            if (name) {
                const newName = name.replace(/schedules\[\d+\]/, `schedules[${index}]`);
                input.setAttribute('name', newName);
            }
        });
    });
}

// Add first schedule on page load
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('add-schedule').click();
});

// Form validation
document.getElementById('smart-link-form').addEventListener('submit', function(e) {
    const schedules = document.querySelectorAll('.schedule-item');
    if (schedules.length === 0) {
        e.preventDefault();
        alert('Vui lòng thêm ít nhất một lịch trình!');
        return false;
    }
    
    // Validate datetime
    let hasError = false;
    schedules.forEach(schedule => {
        const startDate = schedule.querySelector('input[name*="[start_date]"]').value;
        const endDate = schedule.querySelector('input[name*="[end_date]"]').value;
        
        if (startDate && endDate && new Date(startDate) >= new Date(endDate)) {
            hasError = true;
        }
    });
    
    if (hasError) {
        e.preventDefault();
        alert('Thời gian kết thúc phải sau thời gian bắt đầu!');
        return false;
    }
});
</script>
@endpush
@endsection
