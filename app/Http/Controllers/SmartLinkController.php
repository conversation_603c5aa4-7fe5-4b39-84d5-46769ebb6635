<?php

namespace App\Http\Controllers;

use App\Models\SmartLink;
use App\Models\SmartLinkSchedule;
use App\Models\Webinar;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class SmartLinkController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (!auth()->user()->can('webinars index')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }

        $smartLinks = SmartLink::with(['schedules.webinar'])
            ->where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('smart-links.index', compact('smartLinks'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        if (!auth()->user()->can('webinars index')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }

        $webinars = Webinar::where('user_id', Auth::id())
            ->orderBy('title')
            ->get();



        return view('smart-links.create', compact('webinars'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!auth()->user()->can('webinars index')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'custom_code' => 'nullable|string|max:8|unique:smart_links,code',
            'schedules' => 'required|array|min:1',
            'schedules.*.webinar_id' => 'required|exists:webinars,id',
            'schedules.*.schedule_index' => 'required|integer|min:0',
        ]);

        try {
            DB::beginTransaction();

            // Use custom code or generate unique code
            $code = $request->custom_code ?: SmartLink::generateCode();
            $url = url("/s/{$code}");

            // Create smart link
            $smartLink = SmartLink::create([
                'user_id' => Auth::id(),
                'title' => $request->title,
                'description' => $request->description,
                'code' => $code,
                'url' => $url,
                'is_active' => true,
            ]);

            // Create schedules
            foreach ($request->schedules as $index => $scheduleData) {
                SmartLinkSchedule::create([
                    'smart_link_id' => $smartLink->id,
                    'webinar_id' => $scheduleData['webinar_id'],
                    'schedule_index' => $scheduleData['schedule_index'],
                    'sort_order' => $index,
                ]);
            }

            DB::commit();

            return redirect()->route('smart-links.show', $smartLink)
                ->with('success', 'Link Smart đã được tạo thành công!');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                ->with('error', 'Có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(SmartLink $smartLink)
    {
        if (!auth()->user()->can('webinars index')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }

        $this->authorize('view', $smartLink);

        $smartLink->load(['schedules.webinar']);

        return view('smart-links.show', compact('smartLink'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(SmartLink $smartLink)
    {
        if (!auth()->user()->can('webinars index')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }

        $this->authorize('update', $smartLink);

        $webinars = Webinar::where('user_id', Auth::id())
            ->orderBy('title')
            ->get();

        $smartLink->load(['schedules.webinar']);

        return view('smart-links.edit', compact('smartLink', 'webinars'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, SmartLink $smartLink)
    {
        if (!auth()->user()->can('webinars index')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }

        $this->authorize('update', $smartLink);

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'schedules' => 'required|array|min:1',
            'schedules.*.webinar_id' => 'required|exists:webinars,id',
            'schedules.*.schedule_index' => 'required|integer|min:0',
        ]);

        try {
            DB::beginTransaction();

            // Update smart link
            $smartLink->update([
                'title' => $request->title,
                'description' => $request->description,
                'is_active' => $request->has('is_active'),
            ]);

            // Delete existing schedules
            $smartLink->schedules()->delete();

            // Create new schedules
            foreach ($request->schedules as $index => $scheduleData) {
                SmartLinkSchedule::create([
                    'smart_link_id' => $smartLink->id,
                    'webinar_id' => $scheduleData['webinar_id'],
                    'schedule_index' => $scheduleData['schedule_index'],
                    'sort_order' => $index,
                ]);
            }

            DB::commit();

            return redirect()->route('smart-links.index')
                ->with('success', 'Link Smart đã được cập nhật thành công!');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                ->with('error', 'Có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SmartLink $smartLink)
    {
        if (!auth()->user()->can('webinars index')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }

        $this->authorize('delete', $smartLink);

        try {
            $smartLink->delete();
            return redirect()->route('smart-links.index')
                ->with('success', 'Link Smart đã được xóa thành công!');
        } catch (\Exception $e) {
            return back()->with('error', 'Có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    /**
     * Handle smart link redirect.
     */
    public function redirect($code)
    {
        $smartLink = SmartLink::where('code', $code)
            ->where('is_active', true)
            ->with(['schedules.webinar'])
            ->first();

        if (!$smartLink) {
            abort(404, 'Link không tồn tại hoặc đã bị vô hiệu hóa.');
        }

        // Increment click count
        $smartLink->incrementClickCount();

        // Get current webinar
        $currentWebinar = $smartLink->getCurrentWebinar();

        if ($currentWebinar) {
            return redirect($currentWebinar->join_url, 301);
        }

        // If no current webinar, get next upcoming webinar
        $nextWebinar = $smartLink->getNextWebinar();

        if ($nextWebinar) {
            // Show waiting page or redirect to next webinar
            return view('smart-links.waiting', compact('smartLink', 'nextWebinar'));
        }

        // No webinars available
        return view('smart-links.no-webinar', compact('smartLink'));
    }
}
