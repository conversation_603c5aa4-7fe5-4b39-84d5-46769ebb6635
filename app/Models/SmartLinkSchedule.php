<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SmartLinkSchedule extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'smart_link_id',
        'webinar_id',
        'start_date',
        'end_date',
        'description',
        'sort_order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'sort_order' => 'integer',
    ];

    /**
     * Get the smart link that owns the schedule.
     */
    public function smartLink()
    {
        return $this->belongsTo(SmartLink::class);
    }

    /**
     * Get the webinar for this schedule.
     */
    public function webinar()
    {
        return $this->belongsTo(Webinar::class);
    }

    /**
     * Check if this schedule is currently active.
     */
    public function isActive()
    {
        $now = now();
        return $this->start_date <= $now && $this->end_date >= $now;
    }

    /**
     * Check if this schedule is upcoming.
     */
    public function isUpcoming()
    {
        return $this->start_date > now();
    }

    /**
     * Check if this schedule has ended.
     */
    public function hasEnded()
    {
        return $this->end_date < now();
    }

    /**
     * Get status text.
     */
    public function getStatusAttribute()
    {
        if ($this->isActive()) {
            return 'Đang diễn ra';
        } elseif ($this->isUpcoming()) {
            return 'Sắp diễn ra';
        } else {
            return 'Đã kết thúc';
        }
    }

    /**
     * Get status badge class.
     */
    public function getStatusBadgeClassAttribute()
    {
        if ($this->isActive()) {
            return 'badge-success';
        } elseif ($this->isUpcoming()) {
            return 'badge-warning';
        } else {
            return 'badge-secondary';
        }
    }
}
