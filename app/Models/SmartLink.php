<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SmartLink extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'title',
        'description',
        'code',
        'url',
        'is_active',
        'click_count',
        'last_accessed_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'click_count' => 'integer',
        'last_accessed_at' => 'datetime',
    ];

    /**
     * Get the user that owns the smart link.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the schedules for the smart link.
     */
    public function schedules()
    {
        return $this->hasMany(SmartLinkSchedule::class)->orderBy('sort_order');
    }

    /**
     * Generate a unique code for smart link.
     */
    public static function generateCode()
    {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $code = '';

        do {
            $code = '';
            for ($i = 0; $i < 8; $i++) {
                $code .= $characters[rand(0, strlen($characters) - 1)];
            }
        } while (self::where('code', $code)->exists());

        return $code;
    }

    /**
     * Get the current active webinar based on schedule.
     */
    public function getCurrentWebinar()
    {
        $now = now();

        foreach ($this->schedules as $schedule) {
            if ($schedule->isActive()) {
                return $schedule->webinar;
            }
        }

        return null;
    }

    /**
     * Get the next upcoming webinar.
     */
    public function getNextWebinar()
    {
        $upcomingSchedules = $this->schedules->filter(function ($schedule) {
            return $schedule->isUpcoming();
        })->sortBy(function ($schedule) {
            return $schedule->start_date_time;
        });

        $nextSchedule = $upcomingSchedules->first();
        return $nextSchedule ? $nextSchedule->webinar : null;
    }

    /**
     * Increment click count.
     */
    public function incrementClickCount()
    {
        $this->increment('click_count');
        $this->update(['last_accessed_at' => now()]);
    }

    /**
     * Get the full smart link URL.
     */
    public function getFullUrlAttribute()
    {
        return url("/s/{$this->code}");
    }
}
